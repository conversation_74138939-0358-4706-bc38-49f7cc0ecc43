{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ feature_name }} - Feature Locked - CozyWish{% endblock %}

{% block dashboard_title %}{{ feature_name }}{% endblock %}

{% block sidebar_content %}
{% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block dashboard_content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <!-- Feature Locked Message -->
            <div class="card-cw text-center">
                <div class="card-cw-body p-5">
                    <div class="feature-locked-icon mb-4">
                        <i class="fas fa-lock fa-3x text-muted"></i>
                    </div>
                    
                    <h2 class="mb-3">{{ feature_name }}</h2>
                    
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Feature Locked</strong>
                    </div>
                    
                    <p class="text-muted mb-4">
                        {{ feature_description }}
                    </p>
                    
                    <div class="feature-benefits mb-4">
                        <h5 class="mb-3">What you'll get with {{ feature_name }}:</h5>
                        <ul class="list-unstyled">
                            {% for benefit in feature_benefits %}
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                {{ benefit }}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <div class="cta-section">
                        <p class="mb-3">
                            <strong>Ready to unlock this feature?</strong>
                        </p>
                        <a href="{% url 'venues_app:venue_create' %}" class="btn-cw-primary btn-lg">
                            <i class="fas fa-plus-circle me-2"></i>
                            Create Your Venue
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Quick Setup Guide -->
            <div class="card-cw mt-4">
                <div class="card-cw-header">
                    <h3 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        Quick Setup Guide
                    </h3>
                </div>
                <div class="card-cw-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="setup-step text-center">
                                <div class="step-number mb-2">1</div>
                                <h5>Create Venue</h5>
                                <p class="text-muted small">Set up your venue with basic information</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="setup-step text-center">
                                <div class="step-number mb-2">2</div>
                                <h5>Add Services</h5>
                                <p class="text-muted small">List the services you offer with pricing</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="setup-step text-center">
                                <div class="step-number mb-2">3</div>
                                <h5>Start Booking</h5>
                                <p class="text-muted small">Begin accepting bookings from customers</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-locked-icon {
    opacity: 0.6;
}

.setup-step .step-number {
    width: 40px;
    height: 40px;
    background: var(--cw-brand-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-weight: bold;
}

.feature-benefits ul li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.feature-benefits ul li:last-child {
    border-bottom: none;
}

.cta-section {
    background: linear-gradient(135deg, rgba(47, 22, 15, 0.05) 0%, rgba(250, 225, 215, 0.1) 100%);
    border-radius: 0.5rem;
    padding: 2rem;
    margin-top: 2rem;
}
</style>
{% endblock %} 